# Department Grouping Usage Guide

## Overview

The Department Grouping feature allows you to combine multiple departments (like LIQUOR and BEVERAGE) under a single group name for reconciliation reporting. This is particularly useful when you want to treat related departments as a single unit in your reconciliation calculations.

## Step-by-Step Usage

### 1. Access the Configuration

1. Navigate to **Smart Dashboard**
2. Select **Reconciliation** dashboard type
3. Click **Configure Mapping** button
4. In the Department-Category Mapping dialog, click **Configure Groups**

### 2. Create a Department Group

1. Click **Add Group** button
2. Fill in the group details:
   - **Group Name**: Enter the name that will appear in reconciliation reports (e.g., "LIQUOR")
   - **Select Departments**: Choose the departments to group together (e.g., LIQU<PERSON>, BE<PERSON>RA<PERSON>)
   - **Description**: Optional description of the group
   - **Active**: Toggle to enable/disable the group

3. Click **Save Groups**

### 3. Example Configuration

#### Scenario: Group LIQUOR and BEVERAGE departments

**Department-Category Mappings:**
- LIQUOR Department → Categories: ["WHISKEY", "VOD<PERSON>", "RUM", "BRANDY"]
- BEVERAGE Department → Categories: ["JUICES", "AERATED DRINKS", "CRUSHES", "SYRUP"]
- FOOD Department → Categories: ["GROCERY", "DAIRY", "MEAT"]

**Department Group Configuration:**
- Group Name: "LIQUOR"
- Departments: [LIQUOR, BEVERAGE]
- Description: "Combined liquor and beverage reporting"

**Result in Reconciliation:**
- Single "LIQUOR" entry containing all 8 categories
- FOOD remains as separate entry
- Cross-category indents calculated correctly across all grouped categories

### 4. Verify the Configuration

1. Save the group configuration
2. Return to the reconciliation dashboard
3. Run the reconciliation report
4. Verify that:
   - Grouped departments appear as single entries
   - All categories from grouped departments are included
   - Calculations are accurate

## Benefits

### 1. **Simplified Reporting**
- Reduce clutter in reconciliation reports
- Focus on business-relevant groupings
- Easier analysis of related departments

### 2. **Flexible Configuration**
- Create multiple groups as needed
- Groups can be enabled/disabled without losing configuration
- No impact on existing workflows

### 3. **Accurate Calculations**
- All reconciliation formulas remain mathematically correct
- Cross-category indents work seamlessly across grouped departments
- No data loss or calculation errors

## Advanced Usage

### Multiple Groups
You can create multiple department groups:

```
Group 1: "LIQUOR"
- Departments: [LIQUOR, BEVERAGE]

Group 2: "FOOD & SUPPLIES"  
- Departments: [FOOD, NON FOOD]

Group 3: "TOBACCO"
- Departments: [TOBACCO, CIGARETTES]
```

### Partial Grouping
Not all departments need to be grouped:

```
Grouped:
- LIQUOR (contains LIQUOR + BEVERAGE departments)

Individual:
- FOOD (remains as individual department)
- TOBACCO (remains as individual department)
```

## Validation Rules

The system enforces these validation rules:

1. **Unique Group Names**: Each group must have a unique name
2. **No Department Overlap**: A department can only belong to one group
3. **Required Fields**: Group name and at least one department are required
4. **Active Groups Only**: Only active groups are used in reconciliation

## Troubleshooting

### Common Issues

1. **Group Not Appearing in Reconciliation**
   - Check that the group is marked as "Active"
   - Verify that departments in the group have category mappings
   - Ensure reconciliation dashboard is refreshed after group changes

2. **Categories Missing from Grouped Department**
   - Verify that all departments in the group have proper category mappings
   - Check that categories are not mapped to departments outside the group

3. **Calculation Errors**
   - The system maintains mathematical accuracy automatically
   - If you notice discrepancies, verify the underlying data integrity
   - Cross-category indents are calculated across all categories in the group

### Best Practices

1. **Plan Your Groups**: Think about how you want to view your reconciliation reports before creating groups
2. **Test Incrementally**: Create one group at a time and verify results
3. **Document Changes**: Keep track of group configurations for audit purposes
4. **Regular Review**: Periodically review group configurations to ensure they still meet business needs

## Technical Notes

- Groups are stored in session storage (will be moved to database in future versions)
- Grouping is optional and doesn't affect existing functionality
- All existing department-category mappings remain unchanged
- The feature is backward compatible with existing configurations

## Support

If you encounter any issues or need assistance with department grouping:

1. Check this usage guide for common solutions
2. Verify your group configuration follows the validation rules
3. Test with a simple group configuration first
4. Contact support with specific error messages or unexpected behavior

---

This feature provides a powerful way to customize your reconciliation reporting while maintaining the accuracy and integrity of your inventory calculations.
