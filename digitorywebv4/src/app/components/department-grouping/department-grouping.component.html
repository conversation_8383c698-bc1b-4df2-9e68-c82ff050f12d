<div class="department-grouping-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="grouping-content">
    <!-- Header -->
    <div class="grouping-header">
      <h3>Department Grouping Configuration</h3>
      <p class="header-description">
        Group departments together for consolidated reconciliation reporting. 
        For example, group "LIQUOR" and "BEVERAGE" departments under "LIQUOR" for unified reporting.
      </p>
    </div>

    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="groupingForm" class="grouping-form">
      <div formArrayName="groups" class="groups-container">
        <!-- Empty State Message -->
        <div *ngIf="groupsFormArray.controls.length === 0" class="empty-state-message">
          <mat-icon class="empty-icon">group_work</mat-icon>
          <p>No department groups configured</p>
          <p class="empty-subtitle">Click "Add Group" to create your first department group</p>
        </div>

        <!-- Group Rows -->
        <div
          *ngFor="let groupForm of groupsFormArray.controls; let i = index; trackBy: trackByGroupId"
          [formGroupName]="i"
          class="group-row"
        >
          <div class="group-header">
            <mat-icon class="group-icon">group_work</mat-icon>
            <span class="group-title">Group {{ i + 1 }}</span>
            <button 
              mat-icon-button 
              type="button"
              (click)="removeGroup(i)"
              class="remove-group-button"
              [disabled]="isSaving"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>

          <div class="group-fields">
            <!-- Group Name -->
            <mat-form-field appearance="outline" class="group-name-field">
              <mat-label>Group Name</mat-label>
              <input 
                matInput 
                formControlName="groupName"
                placeholder="e.g., LIQUOR"
                (input)="onGroupChange(i)"
              >
              <mat-hint>This name will appear in reconciliation reports</mat-hint>
            </mat-form-field>

            <!-- Department Selection -->
            <mat-form-field appearance="outline" class="departments-field">
              <mat-label>Select Departments</mat-label>
              <mat-select
                formControlName="departmentIds"
                multiple
                (selectionChange)="onGroupChange(i)"
                [disabled]="departments.length === 0"
              >
                <mat-option
                  *ngFor="let department of getAvailableDepartments(i); trackBy: trackByGroupId"
                  [value]="department.id"
                >
                  {{ department.name }}
                </mat-option>
              </mat-select>
              <mat-hint *ngIf="departments.length === 0">No departments available</mat-hint>
              <mat-hint *ngIf="departments.length > 0">Select departments to group together</mat-hint>
            </mat-form-field>

            <!-- Description (Optional) -->
            <mat-form-field appearance="outline" class="description-field">
              <mat-label>Description (Optional)</mat-label>
              <textarea 
                matInput 
                formControlName="description"
                placeholder="Brief description of this group"
                rows="2"
                (input)="onGroupChange(i)"
              ></textarea>
            </mat-form-field>

            <!-- Active Toggle -->
            <div class="active-toggle">
              <mat-slide-toggle 
                formControlName="isActive"
                (change)="onGroupChange(i)"
              >
                Active
              </mat-slide-toggle>
            </div>
          </div>

          <!-- Selected Departments Preview -->
          <div *ngIf="groupForm.get('departmentIds')?.value?.length > 0" class="selected-departments">
            <span class="preview-label">Selected Departments:</span>
            <div class="department-chips">
              <mat-chip-set>
                <mat-chip
                  *ngFor="let deptId of groupForm.get('departmentIds')?.value"
                  class="department-chip"
                >
                  {{ getDepartmentName(deptId) }}
                </mat-chip>
              </mat-chip-set>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Group Button -->
      <div class="add-group-section">
        <button 
          mat-stroked-button 
          type="button"
          (click)="addGroup()"
          [disabled]="isSaving"
          class="add-group-button"
        >
          <mat-icon>add</mat-icon>
          Add Group
        </button>
      </div>
    </form>
  </div>

  <!-- Actions -->
  <div class="grouping-actions">
    <button 
      mat-button 
      type="button"
      (click)="onClose()"
      class="cancel-button"
    >
      Cancel
    </button>
    
    <button 
      mat-raised-button 
      color="primary"
      type="button"
      (click)="saveGroups()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Groups' }}
    </button>
  </div>
</div>
