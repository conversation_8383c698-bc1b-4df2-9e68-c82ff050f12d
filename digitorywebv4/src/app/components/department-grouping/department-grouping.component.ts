import { Component, Input, Output, EventEmitter, OnInit, OnChanges, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';

// Services
import { DepartmentService, Department, DepartmentGroup } from '../../services/department.service';

@Component({
  selector: 'app-department-grouping',
  templateUrl: './department-grouping.component.html',
  styleUrls: ['./department-grouping.component.scss']
})
export class DepartmentGroupingComponent implements OnInit, OnChanges, OnDestroy {
  @Input() tenantId: string = '';
  @Input() departments: Department[] = [];
  @Input() existingGroups: DepartmentGroup[] = [];
  @Output() groupsChanged = new EventEmitter<DepartmentGroup[]>();
  @Output() closeDialog = new EventEmitter<void>();

  // Form and data
  groupingForm: FormGroup;
  groups: DepartmentGroup[] = [];

  // UI state
  isLoading = false;
  isSaving = false;
  validationErrors: string[] = [];

  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.groupingForm = this.fb.group({
      groups: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.loadExistingGroups();
  }

  ngOnChanges(): void {
    if (this.existingGroups) {
      this.groups = [...this.existingGroups];
      this.buildFormFromGroups();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== FORM MANAGEMENT =====

  get groupsFormArray(): FormArray {
    return this.groupingForm.get('groups') as FormArray;
  }

  private loadExistingGroups(): void {
    if (this.tenantId) {
      this.departmentService.getDepartmentGroups(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (groups) => {
            this.groups = groups;
            this.buildFormFromGroups();
          },
          error: () => {
            this.groups = [];
            this.buildFormFromGroups();
          }
        });
    }
  }

  private buildFormFromGroups(): void {
    const groupsArray = this.fb.array([]);
    
    this.groups.forEach(group => {
      const groupForm = this.fb.group({
        groupId: [group.groupId, Validators.required],
        groupName: [group.groupName, Validators.required],
        departmentIds: [group.departmentIds || [], Validators.required],
        description: [group.description || ''],
        isActive: [group.isActive !== false]
      });
      groupsArray.push(groupForm);
    });

    this.groupingForm.setControl('groups', groupsArray);
    this.cdr.detectChanges();
  }

  // ===== GROUP MANAGEMENT =====

  addGroup(): void {
    const newGroup: DepartmentGroup = {
      groupId: this.generateGroupId(),
      groupName: '',
      departmentIds: [],
      description: '',
      isActive: true
    };

    this.groups.push(newGroup);
    
    const groupForm = this.fb.group({
      groupId: [newGroup.groupId, Validators.required],
      groupName: ['', Validators.required],
      departmentIds: [[], Validators.required],
      description: [''],
      isActive: [true]
    });

    this.groupsFormArray.push(groupForm);
    this.cdr.detectChanges();
  }

  removeGroup(index: number): void {
    this.groups.splice(index, 1);
    this.groupsFormArray.removeAt(index);
    this.cdr.detectChanges();
  }

  private generateGroupId(): string {
    return 'group_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // ===== FORM ACTIONS =====

  onGroupChange(index: number): void {
    const groupForm = this.groupsFormArray.at(index) as FormGroup;
    if (groupForm && index < this.groups.length) {
      const formValue = groupForm.value;
      this.groups[index] = {
        ...this.groups[index],
        ...formValue
      };
    }
  }

  // ===== VALIDATION =====

  private validateGroups(): void {
    const validation = this.departmentService.validateGroups(this.groups);
    this.validationErrors = validation.errors;
  }

  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }

  get canSave(): boolean {
    return this.groupingForm.valid && !this.hasValidationErrors && !this.isSaving;
  }

  // ===== SAVE FUNCTIONALITY =====

  saveGroups(): void {
    this.validateGroups();
    
    if (!this.canSave) {
      return;
    }

    this.isSaving = true;

    // Filter out invalid groups
    const validGroups = this.groups.filter(group => 
      group.groupName && group.departmentIds.length > 0
    );

    this.departmentService.saveDepartmentGroups(this.tenantId, validGroups)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.groups = validGroups;
            this.groupsChanged.emit(validGroups);
            this.showSuccess('Department groups saved successfully');
          } else {
            this.showError('Failed to save department groups');
          }
          this.isSaving = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to save department groups');
          this.isSaving = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== UTILITY METHODS =====

  getAvailableDepartments(currentGroupIndex: number): Department[] {
    // Get departments that are not already assigned to other groups
    const assignedDepartmentIds = new Set<string>();
    
    this.groups.forEach((group, index) => {
      if (index !== currentGroupIndex) {
        group.departmentIds.forEach(deptId => assignedDepartmentIds.add(deptId));
      }
    });

    return this.departments.filter(dept => !assignedDepartmentIds.has(dept.id));
  }

  getDepartmentName(departmentId: string): string {
    const department = this.departments.find(d => d.id === departmentId);
    return department ? department.name : 'Unknown Department';
  }

  trackByGroupId(index: number, group: any): string {
    return group.get('groupId')?.value || index.toString();
  }

  // ===== UI HELPERS =====

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  onClose(): void {
    this.closeDialog.emit();
  }
}
