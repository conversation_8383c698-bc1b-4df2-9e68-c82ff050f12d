.department-grouping-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9fa;
}

// Loading State
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// Header Styles
.grouping-header {
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 16px;

  h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 18px;
    font-weight: 500;
  }

  .header-description {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
  }
}

// Content Area
.grouping-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
}

// Validation Errors
.validation-errors {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  margin-bottom: 16px;

  .error-icon {
    color: #856404;
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-top: 2px;
  }

  .error-list {
    flex: 1;

    .error-message {
      margin: 0 0 4px 0;
      color: #856404;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Empty State
.empty-state-message {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;

  .empty-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
  }

  p {
    margin: 0 0 8px 0;
    font-size: 16px;
  }

  .empty-subtitle {
    font-size: 14px;
    color: #adb5bd;
  }
}

// Group Styles
.groups-container {
  margin-bottom: 24px;
}

.group-row {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: #ffb366;
    box-shadow: 0 2px 8px rgba(255, 179, 102, 0.1);
  }
}

.group-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .group-icon {
    color: #ffb366;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .group-title {
    flex: 1;
    font-weight: 500;
    color: #333;
    font-size: 16px;
  }

  .remove-group-button {
    color: #dc3545;
    
    &:hover {
      background-color: rgba(220, 53, 69, 0.1);
    }
  }
}

.group-fields {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: start;

  .group-name-field {
    grid-column: 1;
  }

  .departments-field {
    grid-column: 2;
  }

  .description-field {
    grid-column: 1 / -1;
  }

  .active-toggle {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    margin-top: 8px;
  }

  mat-form-field {
    width: 100%;
    margin: 0;
  }
}

// Selected Departments Preview
.selected-departments {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f1f3f4;

  .preview-label {
    display: block;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .department-chips {
    .department-chip {
      background-color: #e3f2fd;
      color: #1976d2;
      font-size: 12px;
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
}

// Add Group Section
.add-group-section {
  text-align: center;
  margin-bottom: 24px;

  .add-group-button {
    color: #ffb366;
    border-color: #ffb366;
    
    &:hover {
      background-color: rgba(255, 179, 102, 0.1);
    }
  }
}

// Actions
.grouping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
  margin-top: auto;

  .cancel-button {
    color: #6c757d;
  }

  .save-button {
    background-color: #ffb366;
    color: white;
    min-width: 120px;

    &:disabled {
      background-color: #dee2e6;
      color: #6c757d;
    }

    .button-spinner {
      margin-right: 8px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .group-fields {
    grid-template-columns: 1fr;
    gap: 12px;

    .group-name-field,
    .departments-field,
    .description-field {
      grid-column: 1;
    }
  }

  .grouping-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }
}
