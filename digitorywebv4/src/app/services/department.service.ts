import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
  // Optional grouping fields for department consolidation
  groupId?: string;
  groupName?: string;
  displayName?: string; // Name to display in reconciliation (group name if grouped, department name if not)
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
  // Optional grouping fields for reconciliation consolidation
  groupId?: string;
  groupName?: string;
  displayName?: string; // Display name for reconciliation tables
}

export interface DepartmentGroup {
  groupId: string;
  groupName: string;
  departmentIds: string[];
  description?: string;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly engineUrl = environment.engineUrl;

  // Session-level storage for department-category mappings
  private sessionMappings: Map<string, DepartmentCategoryMapping[]> = new Map();

  // Session-level storage for department groups
  private sessionGroups: Map<string, DepartmentGroup[]> = new Map();

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant (via secure backend endpoint)
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/departments/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            const departments = response.data.map((dept: any) => ({
              id: dept.id,
              name: dept.name,
              code: dept.code,
              description: dept.description,
              isActive: dept.isActive !== false
            }));
            return departments;
          }
          throw new Error(response.message || 'Failed to fetch departments');
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching departments:', error);
          return throwError(() => new Error('Failed to fetch departments'));
        })
      );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant (session-level)
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    // Return session mappings if they exist, otherwise return empty array
    const mappings = this.sessionMappings.get(tenantId) || [];

    return new Observable(observer => {
      observer.next(mappings);
      observer.complete();
    });
  }

  /**
   * Save department-category mappings (session-level)
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    // Store mappings in session storage
    this.sessionMappings.set(tenantId, mappings);

    return new Observable(observer => {
      observer.next(true);
      observer.complete();
    });
  }

  /**
   * Get categories mapped to a specific department (session-level)
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category (session-level)
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  /**
   * Clear session mappings for a tenant
   */
  clearSessionMappings(tenantId: string): void {
    this.sessionMappings.delete(tenantId);
  }

  /**
   * Check if session mappings exist for a tenant
   */
  hasSessionMappings(tenantId: string): boolean {
    return this.sessionMappings.has(tenantId);
  }

  // ===== DEPARTMENT GROUPING METHODS =====

  /**
   * Get department groups for a tenant (session-level)
   */
  getDepartmentGroups(tenantId: string): Observable<DepartmentGroup[]> {
    const groups = this.sessionGroups.get(tenantId) || [];
    return new Observable(observer => {
      observer.next(groups);
      observer.complete();
    });
  }

  /**
   * Save department groups (session-level)
   */
  saveDepartmentGroups(tenantId: string, groups: DepartmentGroup[]): Observable<boolean> {
    this.sessionGroups.set(tenantId, groups);
    return new Observable(observer => {
      observer.next(true);
      observer.complete();
    });
  }

  /**
   * Apply department grouping to mappings for reconciliation
   * This method enriches mappings with group information
   */
  applyGroupingToMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<DepartmentCategoryMapping[]> {
    return this.getDepartmentGroups(tenantId).pipe(
      map(groups => {
        if (!groups || groups.length === 0) {
          return mappings; // No grouping configured, return as-is
        }

        // Create department to group lookup
        const deptToGroup = new Map<string, DepartmentGroup>();
        groups.forEach(group => {
          group.departmentIds.forEach(deptId => {
            deptToGroup.set(deptId, group);
          });
        });

        // Apply grouping to mappings
        return mappings.map(mapping => {
          const group = deptToGroup.get(mapping.departmentId);
          if (group) {
            return {
              ...mapping,
              groupId: group.groupId,
              groupName: group.groupName,
              displayName: group.groupName
            };
          }
          return {
            ...mapping,
            displayName: mapping.departmentName
          };
        });
      })
    );
  }

  /**
   * Get consolidated mappings for reconciliation (groups categories by group/department)
   */
  getConsolidatedMappingsForReconciliation(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      switchMap((mappings: DepartmentCategoryMapping[]) => this.applyGroupingToMappings(tenantId, mappings)),
      map((enrichedMappings: DepartmentCategoryMapping[]) => {
        // Group mappings by displayName (group name or department name)
        const consolidatedMap = new Map<string, DepartmentCategoryMapping>();

        enrichedMappings.forEach((mapping: DepartmentCategoryMapping) => {
          const key = mapping.displayName || mapping.departmentName;

          if (consolidatedMap.has(key)) {
            // Merge categories
            const existing = consolidatedMap.get(key)!;
            existing.categories = [...new Set([...existing.categories, ...mapping.categories])];
          } else {
            // Create new consolidated mapping
            consolidatedMap.set(key, {
              departmentId: mapping.groupId || mapping.departmentId,
              departmentName: key,
              categories: [...mapping.categories],
              groupId: mapping.groupId,
              groupName: mapping.groupName,
              displayName: key
            });
          }
        });

        return Array.from(consolidatedMap.values());
      })
    );
  }

  /**
   * Clear session groups for a tenant
   */
  clearSessionGroups(tenantId: string): void {
    this.sessionGroups.delete(tenantId);
  }

  // ===== UTILITY METHODS =====

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Single category can only be mapped to one department
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToDepartment = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToDepartment.has(category)) {
          const existingDept = categoryToDepartment.get(category);
          if (existingDept !== mapping.departmentId) {
            errors.push(`Category "${category}" is mapped to multiple departments: "${existingDept}" and "${mapping.departmentName}"`);
          }
        } else {
          categoryToDepartment.set(category, mapping.departmentId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate department groups
   */
  validateGroups(groups: DepartmentGroup[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const groupIds = new Set<string>();
    const departmentIds = new Set<string>();

    for (const group of groups) {
      // Check for duplicate group IDs
      if (groupIds.has(group.groupId)) {
        errors.push(`Duplicate group ID: "${group.groupId}"`);
      } else {
        groupIds.add(group.groupId);
      }

      // Check for departments in multiple groups
      for (const deptId of group.departmentIds) {
        if (departmentIds.has(deptId)) {
          errors.push(`Department "${deptId}" is assigned to multiple groups`);
        } else {
          departmentIds.add(deptId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
