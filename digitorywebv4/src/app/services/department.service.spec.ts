import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DepartmentService, Department, DepartmentCategoryMapping, DepartmentGroup } from './department.service';

describe('DepartmentService - Department Grouping', () => {
  let service: DepartmentService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DepartmentService]
    });
    service = TestBed.inject(DepartmentService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Department Grouping', () => {
    const mockDepartments: Department[] = [
      { id: 'dept1', name: 'LIQUOR' },
      { id: 'dept2', name: 'BEVERAGE' },
      { id: 'dept3', name: 'FOOD' }
    ];

    const mockMappings: DepartmentCategoryMapping[] = [
      { departmentId: 'dept1', departmentName: 'LIQUOR', categories: ['WHISKEY', 'VODKA'] },
      { departmentId: 'dept2', departmentName: 'BEVERAGE', categories: ['JUICES', 'AERATED DRINKS'] },
      { departmentId: 'dept3', departmentName: 'FOOD', categories: ['GROCERY', 'DAIRY'] }
    ];

    const mockGroups: DepartmentGroup[] = [
      {
        groupId: 'group1',
        groupName: 'LIQUOR',
        departmentIds: ['dept1', 'dept2'], // Group LIQUOR and BEVERAGE departments
        description: 'Combined liquor and beverage department',
        isActive: true
      }
    ];

    it('should save and retrieve department groups', (done) => {
      const tenantId = 'test-tenant';

      service.saveDepartmentGroups(tenantId, mockGroups).subscribe(success => {
        expect(success).toBe(true);

        service.getDepartmentGroups(tenantId).subscribe(groups => {
          expect(groups).toEqual(mockGroups);
          done();
        });
      });
    });

    it('should apply grouping to mappings correctly', (done) => {
      const tenantId = 'test-tenant';

      // First save the groups
      service.saveDepartmentGroups(tenantId, mockGroups).subscribe(() => {
        // Then save the mappings
        service.saveDepartmentCategoryMappings(tenantId, mockMappings).subscribe(() => {
          // Apply grouping to mappings
          service.applyGroupingToMappings(tenantId, mockMappings).subscribe(enrichedMappings => {
            // Check that LIQUOR and BEVERAGE departments now have group information
            const liquorMapping = enrichedMappings.find(m => m.departmentId === 'dept1');
            const beverageMapping = enrichedMappings.find(m => m.departmentId === 'dept2');
            const foodMapping = enrichedMappings.find(m => m.departmentId === 'dept3');

            expect(liquorMapping?.groupId).toBe('group1');
            expect(liquorMapping?.groupName).toBe('LIQUOR');
            expect(liquorMapping?.displayName).toBe('LIQUOR');

            expect(beverageMapping?.groupId).toBe('group1');
            expect(beverageMapping?.groupName).toBe('LIQUOR');
            expect(beverageMapping?.displayName).toBe('LIQUOR');

            expect(foodMapping?.groupId).toBeUndefined();
            expect(foodMapping?.displayName).toBe('FOOD');

            done();
          });
        });
      });
    });

    it('should create consolidated mappings for reconciliation', (done) => {
      const tenantId = 'test-tenant';

      // Save groups and mappings
      service.saveDepartmentGroups(tenantId, mockGroups).subscribe(() => {
        service.saveDepartmentCategoryMappings(tenantId, mockMappings).subscribe(() => {
          // Get consolidated mappings
          service.getConsolidatedMappingsForReconciliation(tenantId).subscribe(consolidatedMappings => {
            // Should have 2 mappings: one for LIQUOR group (combining LIQUOR and BEVERAGE) and one for FOOD
            expect(consolidatedMappings.length).toBe(2);

            const liquorGroupMapping = consolidatedMappings.find(m => m.departmentName === 'LIQUOR');
            const foodMapping = consolidatedMappings.find(m => m.departmentName === 'FOOD');

            // LIQUOR group should contain categories from both LIQUOR and BEVERAGE departments
            expect(liquorGroupMapping?.categories).toContain('WHISKEY');
            expect(liquorGroupMapping?.categories).toContain('VODKA');
            expect(liquorGroupMapping?.categories).toContain('JUICES');
            expect(liquorGroupMapping?.categories).toContain('AERATED DRINKS');
            expect(liquorGroupMapping?.categories.length).toBe(4);

            // FOOD should remain unchanged
            expect(foodMapping?.categories).toEqual(['GROCERY', 'DAIRY']);

            done();
          });
        });
      });
    });

    it('should validate department groups correctly', () => {
      const validGroups: DepartmentGroup[] = [
        { groupId: 'group1', groupName: 'LIQUOR', departmentIds: ['dept1', 'dept2'], isActive: true },
        { groupId: 'group2', groupName: 'FOOD', departmentIds: ['dept3'], isActive: true }
      ];

      const invalidGroups: DepartmentGroup[] = [
        { groupId: 'group1', groupName: 'LIQUOR', departmentIds: ['dept1', 'dept2'], isActive: true },
        { groupId: 'group1', groupName: 'DUPLICATE', departmentIds: ['dept3'], isActive: true }, // Duplicate group ID
        { groupId: 'group3', groupName: 'OVERLAP', departmentIds: ['dept1'], isActive: true } // Department already in group1
      ];

      const validResult = service.validateGroups(validGroups);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors.length).toBe(0);

      const invalidResult = service.validateGroups(invalidGroups);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
      expect(invalidResult.errors.some(error => error.includes('Duplicate group ID'))).toBe(true);
      expect(invalidResult.errors.some(error => error.includes('multiple groups'))).toBe(true);
    });

    it('should handle empty groups gracefully', (done) => {
      const tenantId = 'test-tenant';

      service.saveDepartmentCategoryMappings(tenantId, mockMappings).subscribe(() => {
        // No groups saved, should return original mappings
        service.applyGroupingToMappings(tenantId, mockMappings).subscribe(enrichedMappings => {
          expect(enrichedMappings.length).toBe(mockMappings.length);
          
          // All mappings should have displayName set to departmentName
          enrichedMappings.forEach(mapping => {
            expect(mapping.displayName).toBe(mapping.departmentName);
            expect(mapping.groupId).toBeUndefined();
          });

          done();
        });
      });
    });
  });

  describe('Department Category Mapping Validation', () => {
    it('should validate mappings correctly', () => {
      const validMappings: DepartmentCategoryMapping[] = [
        { departmentId: 'dept1', departmentName: 'LIQUOR', categories: ['WHISKEY', 'VODKA'] },
        { departmentId: 'dept2', departmentName: 'FOOD', categories: ['GROCERY', 'DAIRY'] }
      ];

      const invalidMappings: DepartmentCategoryMapping[] = [
        { departmentId: 'dept1', departmentName: 'LIQUOR', categories: ['WHISKEY', 'VODKA'] },
        { departmentId: 'dept2', departmentName: 'FOOD', categories: ['WHISKEY', 'DAIRY'] } // WHISKEY already mapped to LIQUOR
      ];

      const validResult = service.validateMappings(validMappings);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors.length).toBe(0);

      const invalidResult = service.validateMappings(invalidMappings);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
      expect(invalidResult.errors[0]).toContain('WHISKEY');
      expect(invalidResult.errors[0]).toContain('multiple departments');
    });
  });
});
